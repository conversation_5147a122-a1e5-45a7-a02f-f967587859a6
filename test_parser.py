#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据解析功能
"""

import re
from typing import List, Dict

def parse_markdown_table(file_path: str) -> List[Dict]:
    """
    解析markdown表格文件，提取数据
    """
    data = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配表格行
    # 匹配格式：| 0101 | 哲学 | A+ | 10001 | 北京大学 |
    pattern = r'\|\s*(\d{4})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*(\d+)\s*\|\s*([^|]+)\s*\|'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        discipline_code = match[0].strip()
        discipline_name = match[1].strip()
        evaluation_grade = match[2].strip()
        school_code = match[3].strip()
        school_name = match[4].strip()
        
        data.append({
            'discipline_code': discipline_code,
            'discipline_name': discipline_name,
            'evaluation_grade': evaluation_grade,
            'school_code': school_code,
            'school_name': school_name
        })
    
    return data

def main():
    """
    测试数据解析
    """
    print("=== 数据解析测试 ===\n")
    
    try:
        data = parse_markdown_table("学科评估结果表格.md")
        print(f"✅ 成功解析 {len(data)} 条记录\n")
        
        # 显示前10条数据
        print("📋 前10条数据预览：")
        for i, record in enumerate(data[:10]):
            print(f"{i+1:2d}. {record['discipline_code']} | {record['discipline_name']:12s} | {record['evaluation_grade']:3s} | {record['school_code']} | {record['school_name']}")
        
        if len(data) > 10:
            print(f"\n... 还有 {len(data) - 10} 条记录")
        
        # 统计信息
        print(f"\n📊 数据统计：")
        
        # 按学科统计
        disciplines = {}
        for record in data:
            key = f"{record['discipline_code']}-{record['discipline_name']}"
            disciplines[key] = disciplines.get(key, 0) + 1
        
        print(f"学科数量: {len(disciplines)}")
        print("前5个学科:")
        for i, (discipline, count) in enumerate(list(disciplines.items())[:5]):
            print(f"  {discipline}: {count} 个学校")
        
        # 按评估等级统计
        grades = {}
        for record in data:
            grade = record['evaluation_grade']
            grades[grade] = grades.get(grade, 0) + 1
        
        print(f"\n评估等级分布:")
        for grade in ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-']:
            if grade in grades:
                print(f"  {grade}: {grades[grade]} 个")
        
        # 检查数据完整性
        print(f"\n🔍 数据完整性检查:")
        incomplete_records = 0
        for record in data:
            if not all([record['discipline_code'], record['discipline_name'], 
                       record['evaluation_grade'], record['school_code'], record['school_name']]):
                incomplete_records += 1
        
        if incomplete_records == 0:
            print("✅ 所有记录都完整")
        else:
            print(f"⚠️  发现 {incomplete_records} 条不完整的记录")
        
        print(f"\n🎯 数据准备就绪，可以导入到Supabase！")
        
    except FileNotFoundError:
        print("❌ 找不到文件 '学科评估结果表格.md'")
    except Exception as e:
        print(f"❌ 解析出错: {str(e)}")

if __name__ == "__main__":
    main()
