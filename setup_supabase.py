#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Supabase配置和表结构创建脚本
"""

import os
from supabase import create_client, Client

def get_supabase_credentials():
    """
    获取Supabase凭据
    """
    print("=== Supabase配置 ===")
    print("请提供你的Supabase项目信息：")
    print("（可以在 https://app.supabase.com 项目设置中找到）\n")
    
    url = input("请输入Supabase URL (格式: https://xxx.supabase.co): ").strip()
    key = input("请输入Supabase API Key (anon public key): ").strip()
    
    if not url or not key:
        print("错误：URL和API Key都不能为空")
        return None, None
    
    return url, key

def test_connection(url: str, key: str) -> Client:
    """
    测试Supabase连接
    """
    try:
        supabase = create_client(url, key)
        # 尝试获取表列表来测试连接
        result = supabase.table('pinggu').select("*").limit(1).execute()
        print("✅ Supabase连接测试成功！")
        return supabase
    except Exception as e:
        print(f"❌ Supabase连接测试失败: {str(e)}")
        return None

def create_table_if_not_exists(supabase: Client):
    """
    创建表结构（如果不存在）
    注意：这个函数只是显示SQL，实际创建需要在Supabase控制台执行
    """
    print("\n=== 表结构创建 ===")
    print("请在Supabase SQL编辑器中执行以下SQL来创建表：\n")
    
    sql = """
-- 创建学科评估结果表
CREATE TABLE IF NOT EXISTS pinggu (
    id BIGSERIAL PRIMARY KEY,
    discipline_code VARCHAR(10) NOT NULL COMMENT '一级学科代码',
    discipline_name VARCHAR(100) NOT NULL COMMENT '一级学科名称',
    evaluation_grade VARCHAR(10) NOT NULL COMMENT '评估等级',
    school_code VARCHAR(10) NOT NULL COMMENT '学校代码',
    school_name VARCHAR(100) NOT NULL COMMENT '学校名称',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_pinggu_discipline_code ON pinggu(discipline_code);
CREATE INDEX IF NOT EXISTS idx_pinggu_school_code ON pinggu(school_code);
CREATE INDEX IF NOT EXISTS idx_pinggu_evaluation_grade ON pinggu(evaluation_grade);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_pinggu_discipline_school ON pinggu(discipline_code, school_code);

-- 添加注释
COMMENT ON TABLE pinggu IS '学科评估结果表';
COMMENT ON COLUMN pinggu.discipline_code IS '一级学科代码，如0101';
COMMENT ON COLUMN pinggu.discipline_name IS '一级学科名称，如哲学';
COMMENT ON COLUMN pinggu.evaluation_grade IS '评估等级，如A+、A、A-、B+等';
COMMENT ON COLUMN pinggu.school_code IS '学校代码';
COMMENT ON COLUMN pinggu.school_name IS '学校名称';
"""
    
    print(sql)
    print("\n请复制上述SQL到Supabase控制台的SQL编辑器中执行。")
    
    input("\n执行完成后按回车键继续...")

def save_env_file(url: str, key: str):
    """
    保存环境变量到.env文件
    """
    env_content = f"""# Supabase配置
SUPABASE_URL={url}
SUPABASE_KEY={key}
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ 配置已保存到 .env 文件")
    print("注意：请不要将 .env 文件提交到版本控制系统中！")

def main():
    """
    主函数
    """
    print("=== Supabase数据导入配置工具 ===\n")
    
    # 1. 获取凭据
    url, key = get_supabase_credentials()
    if not url or not key:
        return
    
    # 2. 测试连接
    print("\n正在测试连接...")
    supabase = test_connection(url, key)
    if not supabase:
        print("请检查URL和API Key是否正确")
        return
    
    # 3. 检查表是否存在
    try:
        result = supabase.table('pinggu').select("*").limit(1).execute()
        print("✅ 表 'pinggu' 已存在")
        
        # 检查表结构
        print("\n检查表结构...")
        if len(result.data) > 0:
            print("表中已有数据，字段包括：", list(result.data[0].keys()))
        else:
            print("表为空，准备导入数据")
            
    except Exception as e:
        print(f"❌ 表 'pinggu' 不存在或无法访问: {str(e)}")
        create_table_if_not_exists(supabase)
        return
    
    # 4. 保存配置
    save_env_file(url, key)
    
    print("\n=== 配置完成 ===")
    print("现在你可以运行 'python import_data_to_supabase.py' 来导入数据")

if __name__ == "__main__":
    main()
