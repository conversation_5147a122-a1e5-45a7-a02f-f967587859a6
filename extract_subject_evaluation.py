#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学科评估结果提取脚本
从第五轮学科评估结果.md文件中提取数据，生成markdown表格格式
"""

import re
import html
from typing import List, Tuple, Dict

def parse_evaluation_file(file_path: str) -> List[Dict]:
    """
    解析学科评估文件，提取所有数据

    Args:
        file_path: 文件路径

    Returns:
        包含所有评估数据的列表
    """
    results = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return results

    # 按学科分割内容
    sections = content.split('# 一级学科代码及名称：')

    for section in sections[1:]:  # 跳过第一个空的部分
        lines = section.strip().split('\n')
        if not lines:
            continue

        # 提取学科代码和名称
        first_line = lines[0]
        subject_match = re.match(r'(\d{4})(.+)', first_line)
        if not subject_match:
            continue

        subject_code = subject_match.group(1)
        subject_name = subject_match.group(2).strip()

        print(f"处理学科: {subject_code} {subject_name}")

        # 重新组合该学科的完整内容
        section_content = '\n'.join(lines)

        # 在该学科内容中查找所有表格
        table_pattern = r'<table>.*?</table>'
        tables = re.findall(table_pattern, section_content, re.DOTALL)

        for table in tables:
            # 解析表格内容
            parsed_data = parse_table(table, subject_code, subject_name)
            results.extend(parsed_data)

    return results

def parse_table(table_html: str, subject_code: str, subject_name: str) -> List[Dict]:
    """
    解析单个HTML表格
    
    Args:
        table_html: HTML表格字符串
        subject_code: 学科代码
        subject_name: 学科名称
        
    Returns:
        解析后的数据列表
    """
    results = []
    
    # 移除HTML标签，获取纯文本内容
    # 先处理表格行
    row_pattern = r'<tr>(.*?)</tr>'
    rows = re.findall(row_pattern, table_html, re.DOTALL)
    
    current_grade = None
    
    for row in rows:
        # 解析单元格
        cell_pattern = r'<td[^>]*>(.*?)</td>'
        cells = re.findall(cell_pattern, row, re.DOTALL)
        
        if len(cells) < 2:
            continue
            
        # 清理HTML内容
        cleaned_cells = []
        for cell in cells:
            # 移除HTML标签并解码HTML实体
            clean_cell = re.sub(r'<[^>]+>', '', cell)
            clean_cell = html.unescape(clean_cell).strip()
            cleaned_cells.append(clean_cell)
        
        # 跳过表头
        if cleaned_cells[0] == '评估结果' or cleaned_cells[0] == '学校代码及名称':
            continue
        
        # 处理评估等级
        if cleaned_cells[0] in ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-']:
            current_grade = cleaned_cells[0]
            school_info = cleaned_cells[1] if len(cleaned_cells) > 1 else ''
        else:
            # 如果第一列不是评估等级，说明是延续上一行的等级
            school_info = cleaned_cells[0]
        
        if not current_grade or not school_info:
            continue
            
        # 解析学校信息
        # 学校信息可能包含多个学校，每行一个
        school_lines = school_info.split('\n')
        for line in school_lines:
            line = line.strip()
            if not line:
                continue
                
            # 匹配学校代码和名称的模式：数字 + 空格 + 学校名称
            school_match = re.match(r'(\d+)\s+(.+)', line)
            if school_match:
                school_code = school_match.group(1)
                school_name = school_match.group(2).strip()
                
                results.append({
                    'subject_code': subject_code,
                    'subject_name': subject_name,
                    'grade': current_grade,
                    'school_code': school_code,
                    'school_name': school_name
                })
    
    return results

def generate_markdown_table(data: List[Dict]) -> str:
    """
    生成markdown表格
    
    Args:
        data: 数据列表
        
    Returns:
        markdown表格字符串
    """
    if not data:
        return ""
    
    # 表头
    header = "| 一级学科代码 | 一级学科名称 | 评估等级 | 学校代码 | 学校名称 |\n"
    separator = "|---|---|---|---|---|\n"
    
    # 数据行
    rows = []
    for item in data:
        row = f"| {item['subject_code']} | {item['subject_name']} | {item['grade']} | {item['school_code']} | {item['school_name']} |"
        rows.append(row)
    
    return header + separator + "\n".join(rows)

def main():
    """主函数"""
    input_file = "高考资料/第五轮学科评估结果.md"
    output_file = "学科评估结果表格.md"
    
    print("开始解析学科评估文件...")
    
    try:
        # 解析文件
        data = parse_evaluation_file(input_file)
        print(f"共提取到 {len(data)} 条记录")
        
        # 生成markdown表格
        markdown_table = generate_markdown_table(data)
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 第五轮学科评估结果汇总表\n\n")
            f.write(markdown_table)
        
        print(f"结果已保存到 {output_file}")
        
        # 显示前10条记录作为预览
        print("\n前10条记录预览：")
        preview_data = data[:10]
        preview_table = generate_markdown_table(preview_data)
        print(preview_table)
        
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
