#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版学科评估结果数据导入Supabase脚本
直接在脚本中配置Supabase连接信息
"""

import re
from supabase import create_client, Client
from typing import List, Dict

# ========== 配置区域 ==========
# 请在这里填入你的Supabase项目信息
SUPABASE_URL = "https://your-project.supabase.co"  # 替换为你的Supabase URL
SUPABASE_KEY = "your-anon-key"  # 替换为你的anon public key
TABLE_NAME = "pinggu"  # 表名

# ========== 脚本代码 ==========

def parse_markdown_table(file_path: str) -> List[Dict]:
    """
    解析markdown表格文件，提取数据
    """
    data = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配表格行
    # 匹配格式：| 0101 | 哲学 | A+ | 10001 | 北京大学 |
    pattern = r'\|\s*(\d{4})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*(\d+)\s*\|\s*([^|]+)\s*\|'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        discipline_code = match[0].strip()
        discipline_name = match[1].strip()
        evaluation_grade = match[2].strip()
        school_code = match[3].strip()
        school_name = match[4].strip()
        
        data.append({
            'discipline_code': discipline_code,
            'discipline_name': discipline_name,
            'evaluation_grade': evaluation_grade,
            'school_code': school_code,
            'school_name': school_name
        })
    
    return data

def create_supabase_client() -> Client:
    """
    创建Supabase客户端
    """
    if SUPABASE_URL == "https://your-project.supabase.co" or SUPABASE_KEY == "your-anon-key":
        print("❌ 错误：请先在脚本顶部配置你的Supabase URL和API Key")
        print("请编辑 simple_import.py 文件，修改 SUPABASE_URL 和 SUPABASE_KEY 变量")
        exit(1)
    
    return create_client(SUPABASE_URL, SUPABASE_KEY)

def batch_insert_data(supabase: Client, table_name: str, data: List[Dict], batch_size: int = 100):
    """
    批量插入数据到Supabase表
    """
    total_records = len(data)
    print(f"开始导入 {total_records} 条记录到表 '{table_name}'...")
    
    success_count = 0
    error_count = 0
    
    # 分批插入数据
    for i in range(0, total_records, batch_size):
        batch = data[i:i + batch_size]
        
        try:
            result = supabase.table(table_name).insert(batch).execute()
            success_count += len(batch)
            print(f"✅ 成功插入第 {i//batch_size + 1} 批数据，共 {len(batch)} 条记录")
            
        except Exception as e:
            error_count += len(batch)
            print(f"❌ 插入第 {i//batch_size + 1} 批数据时出错: {str(e)}")
            
            # 尝试逐条插入以找出问题记录
            print("尝试逐条插入...")
            for j, record in enumerate(batch):
                try:
                    supabase.table(table_name).insert(record).execute()
                    success_count += 1
                    error_count -= 1
                    print(f"  ✅ 记录 {i+j+1} 插入成功")
                except Exception as single_error:
                    print(f"  ❌ 记录 {i+j+1} 插入失败: {record}")
                    print(f"     错误: {str(single_error)}")
    
    print(f"\n=== 导入完成 ===")
    print(f"✅ 成功导入: {success_count} 条记录")
    print(f"❌ 失败: {error_count} 条记录")
    
    return success_count, error_count

def main():
    """
    主函数
    """
    print("=== 学科评估结果数据导入工具（简化版）===\n")
    
    # 1. 解析markdown文件
    markdown_file = "学科评估结果表格.md"
    
    print("📖 正在解析markdown文件...")
    try:
        data = parse_markdown_table(markdown_file)
        print(f"✅ 解析完成，共找到 {len(data)} 条记录\n")
    except FileNotFoundError:
        print(f"❌ 错误：找不到文件 {markdown_file}")
        print("请确保文件存在于当前目录中")
        return
    except Exception as e:
        print(f"❌ 解析文件时出错: {str(e)}")
        return
    
    if len(data) == 0:
        print("❌ 没有找到有效数据，请检查文件格式")
        return
    
    # 显示前几条数据作为预览
    print("📋 数据预览（前5条）：")
    for i, record in enumerate(data[:5]):
        print(f"  {i+1}. 学科：{record['discipline_name']} | 学校：{record['school_name']} | 等级：{record['evaluation_grade']}")
    
    if len(data) > 5:
        print(f"  ... 还有 {len(data) - 5} 条记录")
    print()
    
    # 2. 创建Supabase客户端
    print("🔗 正在连接Supabase...")
    try:
        supabase = create_supabase_client()
        print("✅ Supabase客户端创建成功\n")
    except Exception as e:
        print(f"❌ 创建Supabase客户端失败: {str(e)}")
        return
    
    # 3. 测试连接
    print("🧪 测试数据库连接...")
    try:
        # 尝试查询表结构
        result = supabase.table(TABLE_NAME).select("*").limit(1).execute()
        print(f"✅ 成功连接到表 '{TABLE_NAME}'")
        
        if len(result.data) > 0:
            print(f"⚠️  表中已有数据，当前记录数: {len(result.data)}")
            print("注意：导入可能会产生重复数据")
        else:
            print("📝 表为空，准备导入数据")
            
    except Exception as e:
        print(f"❌ 连接表 '{TABLE_NAME}' 失败: {str(e)}")
        print("请确保：")
        print("1. 表名正确")
        print("2. 表已经创建")
        print("3. API Key有正确的权限")
        return
    
    print()
    
    # 4. 确认导入
    print(f"📊 准备导入 {len(data)} 条记录到表 '{TABLE_NAME}'")
    confirm = input("确认继续吗？(输入 'y' 或 'yes' 继续): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return
    
    print()
    
    # 5. 执行批量插入
    success_count, error_count = batch_insert_data(supabase, TABLE_NAME, data)
    
    # 6. 显示结果
    if error_count == 0:
        print("\n🎉 所有数据导入成功！")
        print(f"你可以在Supabase控制台查看导入的 {success_count} 条记录")
    else:
        print(f"\n⚠️  导入完成，但有 {error_count} 条记录失败")
        print("请检查错误信息并手动处理失败的记录")

if __name__ == "__main__":
    main()
