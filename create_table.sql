-- 创建学科评估结果表
-- 请在Supabase控制台的SQL编辑器中执行此脚本

-- 删除表（如果需要重新创建）
-- DROP TABLE IF EXISTS pinggu;

-- 创建表
CREATE TABLE IF NOT EXISTS pinggu (
    id BIGSERIAL PRIMARY KEY,
    discipline_code VARCHAR(10) NOT NULL COMMENT '一级学科代码',
    discipline_name VARCHAR(100) NOT NULL COMMENT '一级学科名称',
    evaluation_grade VARCHAR(10) NOT NULL COMMENT '评估等级',
    school_code VARCHAR(10) NOT NULL COMMENT '学校代码',
    school_name VARCHAR(100) NOT NULL COMMENT '学校名称',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_pinggu_discipline_code ON pinggu(discipline_code);
CREATE INDEX IF NOT EXISTS idx_pinggu_school_code ON pinggu(school_code);
CREATE INDEX IF NOT EXISTS idx_pinggu_evaluation_grade ON pinggu(evaluation_grade);
CREATE INDEX IF NOT EXISTS idx_pinggu_discipline_school ON pinggu(discipline_code, school_code);

-- 添加表注释
COMMENT ON TABLE pinggu IS '学科评估结果表';
COMMENT ON COLUMN pinggu.discipline_code IS '一级学科代码，如0101';
COMMENT ON COLUMN pinggu.discipline_name IS '一级学科名称，如哲学';
COMMENT ON COLUMN pinggu.evaluation_grade IS '评估等级，如A+、A、A-、B+等';
COMMENT ON COLUMN pinggu.school_code IS '学校代码';
COMMENT ON COLUMN pinggu.school_name IS '学校名称';

-- 查看表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'pinggu' 
ORDER BY ordinal_position;
