#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学科评估结果数据导入Supabase脚本
"""

import re
import os
from supabase import create_client, Client
from typing import List, Dict

# 尝试加载 python-dotenv，如果没有安装则使用默认配置
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("注意：未安装 python-dotenv，将使用默认配置")

def parse_markdown_table(file_path: str) -> List[Dict]:
    """
    解析markdown表格文件，提取数据
    """
    data = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配表格行
    # 匹配格式：| 0101 | 哲学 | A+ | 10001 | 北京大学 |
    pattern = r'\|\s*(\d{4})\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*(\d+)\s*\|\s*([^|]+)\s*\|'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        discipline_code = match[0].strip()
        discipline_name = match[1].strip()
        evaluation_grade = match[2].strip()
        school_code = match[3].strip()
        school_name = match[4].strip()
        
        data.append({
            'discipline_code': discipline_code,
            'discipline_name': discipline_name,
            'evaluation_grade': evaluation_grade,
            'school_code': school_code,
            'school_name': school_name
        })
    
    return data

def create_supabase_client() -> Client:
    """
    创建Supabase客户端
    需要设置环境变量：
    - SUPABASE_URL: 你的Supabase项目URL
    - SUPABASE_KEY: 你的Supabase API密钥
    """
    url = os.environ.get("SUPABASE_URL")
    key = os.environ.get("SUPABASE_KEY")
    
    if not url or not key:
        print("错误：请设置环境变量 SUPABASE_URL 和 SUPABASE_KEY")
        print("你可以在Supabase项目设置中找到这些信息")
        exit(1)
    
    return create_client(url, key)

def batch_insert_data(supabase: Client, table_name: str, data: List[Dict], batch_size: int = 100):
    """
    批量插入数据到Supabase表
    """
    total_records = len(data)
    print(f"开始导入 {total_records} 条记录到表 '{table_name}'...")
    
    success_count = 0
    error_count = 0
    
    # 分批插入数据
    for i in range(0, total_records, batch_size):
        batch = data[i:i + batch_size]
        
        try:
            result = supabase.table(table_name).insert(batch).execute()
            success_count += len(batch)
            print(f"成功插入第 {i//batch_size + 1} 批数据，共 {len(batch)} 条记录")
            
        except Exception as e:
            error_count += len(batch)
            print(f"插入第 {i//batch_size + 1} 批数据时出错: {str(e)}")
            
            # 尝试逐条插入以找出问题记录
            for record in batch:
                try:
                    supabase.table(table_name).insert(record).execute()
                    success_count += 1
                    error_count -= 1
                except Exception as single_error:
                    print(f"插入记录失败: {record}, 错误: {str(single_error)}")
    
    print(f"\n导入完成！")
    print(f"成功导入: {success_count} 条记录")
    print(f"失败: {error_count} 条记录")
    
    return success_count, error_count

def main():
    """
    主函数
    """
    print("=== 学科评估结果数据导入工具 ===\n")
    
    # 1. 解析markdown文件
    markdown_file = "学科评估结果表格.md"
    if not os.path.exists(markdown_file):
        print(f"错误：找不到文件 {markdown_file}")
        return
    
    print("正在解析markdown文件...")
    data = parse_markdown_table(markdown_file)
    print(f"解析完成，共找到 {len(data)} 条记录\n")
    
    if len(data) == 0:
        print("没有找到有效数据，请检查文件格式")
        return
    
    # 显示前几条数据作为预览
    print("数据预览（前5条）：")
    for i, record in enumerate(data[:5]):
        print(f"{i+1}. {record}")
    print()
    
    # 2. 创建Supabase客户端
    try:
        supabase = create_supabase_client()
        print("Supabase客户端创建成功\n")
    except Exception as e:
        print(f"创建Supabase客户端失败: {str(e)}")
        return
    
    # 3. 导入数据
    table_name = "pinggu"  # 你的表名
    
    # 确认是否继续
    confirm = input(f"确认要将 {len(data)} 条记录导入到表 '{table_name}' 吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    # 执行批量插入
    success_count, error_count = batch_insert_data(supabase, table_name, data)
    
    if error_count == 0:
        print("\n🎉 所有数据导入成功！")
    else:
        print(f"\n⚠️  导入完成，但有 {error_count} 条记录失败")

if __name__ == "__main__":
    main()
