# 快速开始指南

## 🚀 最简单的使用方法

### 第一步：安装依赖
```bash
pip install supabase
```

### 第二步：获取Supabase信息
1. 打开 [Supabase控制台](https://app.supabase.com)
2. 选择你的项目 `songweibiao`
3. 点击左侧 "Settings" → "API"
4. 复制：
   - **Project URL** (类似: `https://xxx.supabase.co`)
   - **anon public key** (很长的字符串)

### 第三步：创建数据库表
在Supabase控制台的 "SQL Editor" 中执行：

```sql
CREATE TABLE IF NOT EXISTS pinggu (
    id BIGSERIAL PRIMARY KEY,
    discipline_code VARCHAR(10) NOT NULL,
    discipline_name VARCHAR(100) NOT NULL,
    evaluation_grade VARCHAR(10) NOT NULL,
    school_code VARCHAR(10) NOT NULL,
    school_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 第四步：配置并运行导入脚本
1. 编辑 `simple_import.py` 文件
2. 修改顶部的配置：
   ```python
   SUPABASE_URL = "https://你的项目.supabase.co"
   SUPABASE_KEY = "你的anon-key"
   ```
3. 运行脚本：
   ```bash
   python simple_import.py
   ```

## ✅ 完成！

导入成功后，你可以在Supabase控制台的 "Table Editor" 中查看数据。

## 🔧 如果遇到问题

### 常见错误及解决方法：

**1. 找不到文件**
- 确保 `学科评估结果表格.md` 在当前目录

**2. 连接失败**
- 检查URL和API Key是否正确
- 确认网络连接正常

**3. 表不存在**
- 在Supabase控制台创建表
- 检查表名是否为 `pinggu`

**4. 权限错误**
- 确认使用的是 anon public key
- 检查表的RLS（行级安全）设置

## 📊 数据预览

导入的数据包含：
- 学科代码和名称
- 评估等级（A+, A, A-, B+, B, B-, C+, C, C-）
- 学校代码和名称
- 共约4000+条记录

## 🎯 下一步

数据导入后，你可以：
1. 在Supabase中创建API接口
2. 构建前端应用展示数据
3. 添加搜索和筛选功能
4. 创建数据可视化图表
