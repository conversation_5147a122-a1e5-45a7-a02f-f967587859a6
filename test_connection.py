#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Supabase连接
"""

from supabase import create_client, Client

# 配置信息
SUPABASE_URL = "https://clliichshouimyiqkjte.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNsbGlpY2hzaG91aW15aXFranRlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0NTI2NzIsImV4cCI6MjA2NTAyODY3Mn0.3H5gIR9_C4IaUdUM920ICpwOOJAnTKr18f4wsAYwiyI"

def test_connection():
    """
    测试Supabase连接
    """
    print("=== Supabase连接测试 ===\n")
    
    try:
        # 创建客户端
        print("🔗 正在创建Supabase客户端...")
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ 客户端创建成功")
        
        # 测试连接 - 尝试查询表
        print("🧪 测试数据库连接...")
        try:
            result = supabase.table('pinggu').select("*").limit(1).execute()
            print("✅ 成功连接到表 'pinggu'")
            
            if len(result.data) > 0:
                print(f"📊 表中已有 {len(result.data)} 条数据")
                print("示例数据:", result.data[0])
            else:
                print("📝 表为空，准备导入数据")
                
        except Exception as table_error:
            print(f"⚠️  表 'pinggu' 不存在或无法访问: {str(table_error)}")
            print("需要先创建表，请在Supabase控制台执行以下SQL：")
            print("""
CREATE TABLE IF NOT EXISTS pinggu (
    id BIGSERIAL PRIMARY KEY,
    discipline_code VARCHAR(10) NOT NULL,
    discipline_name VARCHAR(100) NOT NULL,
    evaluation_grade VARCHAR(10) NOT NULL,
    school_code VARCHAR(10) NOT NULL,
    school_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
            """)
        
        print("\n🎉 连接测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        print("请检查：")
        print("1. URL是否正确")
        print("2. API Key是否正确")
        print("3. 网络连接是否正常")
        return False

if __name__ == "__main__":
    test_connection()
